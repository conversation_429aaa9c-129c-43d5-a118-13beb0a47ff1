import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query';

import { EmployeeService } from '../services/employee.service';
import {
  AssignRoleDto,
  CreateEmployeeDto,
  CreateEmployeeWithUserDto,
  EmployeeQueryDto,
  UpdateEmployeeDto,
} from '../types/employee.types';

// Key cho React Query
const EMPLOYEES_QUERY_KEY = 'employees';
const USERS_QUERY_KEY = 'users';

/**
 * Interface cho kết quả phân trang
 */
interface PaginatedSearchResult<T> {
  items: T[];
  meta?: {
    totalItems: number;
    itemCount: number;
    itemsPerPage: number;
    totalPages: number;
    currentPage: number;
  };
}

/**
 * Interface cho tham số phân trang
 */
interface PaginationParams {
  page: number;
  limit: number;
}

/**
 * Hook để lấy danh sách nhân viên
 * @param params Tham số truy vấn
 * @returns Query result với danh sách nhân viên
 */
export const useEmployees = (params?: EmployeeQueryDto) => {
  return useQuery({
    queryKey: [EMPLOYEES_QUERY_KEY, params],
    queryFn: () => EmployeeService.getEmployees(params),
    select: data => data.result,
  });
};

/**
 * Hook để lấy chi tiết nhân viên
 * @param id ID nhân viên
 * @returns Query result với chi tiết nhân viên
 */
export const useEmployee = (id: number) => {
  return useQuery({
    queryKey: [EMPLOYEES_QUERY_KEY, id],
    queryFn: () => EmployeeService.getEmployee(id),
    select: data => data.result,
    enabled: !!id,
  });
};

/**
 * Hook để lấy nhân viên theo ID người dùng
 * @param userId ID người dùng
 * @returns Query result với chi tiết nhân viên
 */
export const useEmployeeByUserId = (userId: number) => {
  return useQuery({
    queryKey: [EMPLOYEES_QUERY_KEY, 'user', userId],
    queryFn: () => EmployeeService.getEmployeeByUserId(userId),
    select: data => data.result,
    enabled: !!userId,
  });
};

/**
 * Hook để tạo nhân viên mới
 * @returns Mutation result cho việc tạo nhân viên
 */
export const useCreateEmployee = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (data: CreateEmployeeDto) => EmployeeService.createEmployee(data),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: [EMPLOYEES_QUERY_KEY] });
    },
  });
};

/**
 * Hook để tạo nhân viên kèm tài khoản người dùng
 * @returns Mutation result cho việc tạo nhân viên kèm tài khoản người dùng
 */
export const useCreateEmployeeWithUser = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (data: CreateEmployeeWithUserDto) => EmployeeService.createEmployeeWithUser(data),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: [EMPLOYEES_QUERY_KEY] });
    },
  });
};

/**
 * Hook để cập nhật nhân viên
 * @returns Mutation result cho việc cập nhật nhân viên
 */
export const useUpdateEmployee = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: ({ id, data }: { id: number; data: UpdateEmployeeDto }) =>
      EmployeeService.updateEmployee(id, data),
    onSuccess: (_, variables) => {
      queryClient.invalidateQueries({ queryKey: [EMPLOYEES_QUERY_KEY] });
      queryClient.invalidateQueries({ queryKey: [EMPLOYEES_QUERY_KEY, variables.id] });
    },
  });
};

/**
 * Hook để xóa nhân viên
 * @returns Mutation result cho việc xóa nhân viên
 */
export const useDeleteEmployee = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (id: number) => EmployeeService.deleteEmployee(id),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: [EMPLOYEES_QUERY_KEY] });
    },
  });
};

/**
 * Hook để phân quyền cho nhân viên
 * @returns Mutation result cho việc phân quyền
 */
export const useAssignRole = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: ({ id, data }: { id: number; data: AssignRoleDto }) =>
      EmployeeService.assignRole(id, data),
    onSuccess: (_, variables) => {
      queryClient.invalidateQueries({ queryKey: [EMPLOYEES_QUERY_KEY] });
      queryClient.invalidateQueries({ queryKey: [EMPLOYEES_QUERY_KEY, variables.id] });
    },
  });
};

/**
 * Hook để gán nhân viên vào phòng ban
 * @returns Mutation result cho việc gán phòng ban
 */
export const useAssignToDepartment = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: ({ id, departmentId }: { id: number; departmentId: number }) =>
      EmployeeService.assignToDepartment(id, departmentId),
    onSuccess: (_, variables) => {
      queryClient.invalidateQueries({ queryKey: [EMPLOYEES_QUERY_KEY] });
      queryClient.invalidateQueries({ queryKey: [EMPLOYEES_QUERY_KEY, variables.id] });
    },
  });
};

/**
 * Hook để gán quản lý cho nhân viên
 * @returns Mutation result cho việc gán quản lý
 */
export const useAssignManager = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: ({ id, managerId }: { id: number; managerId: number }) =>
      EmployeeService.assignManager(id, managerId),
    onSuccess: (_, variables) => {
      queryClient.invalidateQueries({ queryKey: [EMPLOYEES_QUERY_KEY] });
      queryClient.invalidateQueries({ queryKey: [EMPLOYEES_QUERY_KEY, variables.id] });
    },
  });
};

/**
 * Hàm tìm kiếm nhân viên với lazy loading và hỗ trợ phân trang
 * @param searchTerm Từ khóa tìm kiếm
 * @param pagination Tham số phân trang (tùy chọn)
 * @returns Promise với danh sách nhân viên dạng SearchItem hoặc kết quả phân trang
 */
export const searchEmployees = async (
  searchTerm: string,
  pagination?: PaginationParams
): Promise<SearchItem[] | PaginatedSearchResult<SearchItem>> => {
  try {
    // Nếu không có từ khóa tìm kiếm và không yêu cầu tải mặc định, trả về mảng rỗng
    if (!searchTerm.trim() && !pagination) {
      return [];
    }

    // Gọi API tìm kiếm nhân viên với phân trang
    const response = await EmployeeService.getEmployees({
      search: searchTerm.trim() || undefined,
      page: pagination?.page || 1,
      limit: pagination?.limit || 10,
    });

    // Chuyển đổi kết quả sang định dạng SearchItem
    const items = response.result.items.map(employee => ({
      id: employee.id,
      name: `${employee.employeeName} - ${employee.fullName || 'N/A'}`,
      description: employee.jobTitle || '',
      disabled: false,
    }));

    // Nếu có yêu cầu phân trang, trả về kết quả phân trang
    if (pagination) {
      return {
        items,
        meta: response.result.meta,
      };
    }

    // Nếu không, chỉ trả về danh sách items
    return items;
  } catch (error) {
    console.error('Error searching employees:', error);
    return [];
  }
};

/**
 * Hàm load nhân viên cho AsyncSelectWithPagination
 * @param params Tham số tìm kiếm và phân trang
 * @returns Promise với kết quả phân trang SelectOption
 */
export const loadEmployeesForAsyncSelect = async (params: {
  search?: string;
  page?: number;
  limit?: number;
}): Promise<{
  items: SelectOption[];
  totalItems: number;
  totalPages: number;
  currentPage: number;
}> => {
  try {
    const { search = '', page = 1, limit = 20 } = params;

    // Gọi API tìm kiếm nhân viên với phân trang
    const response = await EmployeeService.getEmployees({
      search: search.trim() || undefined,
      page,
      limit,
    });

    // Chuyển đổi kết quả sang định dạng SelectOption
    const items: SelectOption[] = response.result.items.map(employee => ({
      value: employee.id,
      label: `${employee.employeeName} - ${employee.fullName || 'N/A'}`,
      data: {
        employeeName: employee.employeeName,
        fullName: employee.fullName,
        jobTitle: employee.jobTitle,
        departmentId: employee.departmentId,
        status: employee.status,
      },
    }));

    return {
      items,
      totalItems: response.result.meta.totalItems,
      totalPages: response.result.meta.totalPages,
      currentPage: response.result.meta.currentPage,
    };
  } catch (error) {
    console.error('Error loading employees for AsyncSelect:', error);
    return {
      items: [],
      totalItems: 0,
      totalPages: 0,
      currentPage: 1,
    };
  }
};

/**
 * Hàm load nhân viên theo ID để tạo initial options
 * @param employeeId ID nhân viên
 * @returns Promise với SelectOption hoặc null
 */
export const loadEmployeeById = async (employeeId: number): Promise<SelectOption | null> => {
  try {
    const response = await EmployeeService.getEmployee(employeeId);
    const employee = response.result;

    return {
      value: employee.id,
      label: `${employee.employeeName} - ${employee.fullName || 'N/A'}`,
      data: {
        employeeName: employee.employeeName,
        fullName: employee.fullName,
        jobTitle: employee.jobTitle,
        departmentId: employee.departmentId,
        status: employee.status,
      },
    };
  } catch (error) {
    console.error('Error loading employee by ID:', error);
    return null;
  }
};
